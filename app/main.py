from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from app.api.endpoints import auth, tress
from app.core.config import settings
from app.db.base import engine, Base

app = FastAPI(title=settings.PROJECT_NAME)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(tress.router, prefix="/api/tress", tags=["tress"])


@app.on_event("startup")
async def init_db():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


@app.get("/")
async def root():
    return {"message": "Welcome to Tressa API"}
