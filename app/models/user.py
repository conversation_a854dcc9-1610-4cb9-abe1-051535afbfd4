from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String
from sqlalchemy.orm import relationship
from app.db.base import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    username = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)

    # 添加关系
    tresses = relationship("Tress", back_populates="owner", cascade="all, delete-orphan")
